package com.bbh.live.service.order.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Map;

/**
 * 支付流水与子订单映射关系DTO
 * 用于缓存payment对应的order_sub信息
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaymentOrderSubMappingDTO {
    
    /**
     * 支付流水号
     */
    private String outTradeNo;
    
    /**
     * 子订单ID与子订单号的映射关系
     * key: order_sub的id
     * value: order_sub_no
     */
    private Map<Long, String> subOrderNoMap;
    
    /**
     * 总订单ID
     */
    private Long globalOrderId;
    
    /**
     * 创建时间戳
     */
    private Long createTime;
}
