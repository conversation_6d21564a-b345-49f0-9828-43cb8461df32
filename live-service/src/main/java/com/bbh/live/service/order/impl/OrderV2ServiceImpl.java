package com.bbh.live.service.order.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.enums.*;
import com.bbh.exception.ServiceException;
import com.bbh.live.config.LiveServiceProperties;
import com.bbh.live.constant.RedisKey;
import com.bbh.live.controller.req.LiveGoodsQueryReq;
import com.bbh.live.dao.dto.*;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.dto.vo.CreateOrderV2VO;
import com.bbh.live.dao.mapper.GlobalOrderPaymentMapper;
import com.bbh.live.dao.mapper.LiveGoodsCancelApplyMapper;
import com.bbh.live.dao.mapper.LiveGoodsMapper;
import com.bbh.live.dao.service.*;
import com.bbh.live.dao.service.impl.GlobalOrderQualityInspectService;
import com.bbh.live.enums.BuyerVipTypeEnum;
import com.bbh.live.enums.LiveOrderCacheKeys;
import com.bbh.live.enums.RedPointTypeEnum;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.msg.MsgService;
import com.bbh.live.service.order.CartService;
import com.bbh.live.service.order.OrderV2Service;
import com.bbh.live.service.order.PayService;
import com.bbh.live.service.order.dto.OfflinePayResultDTO;
import com.bbh.live.service.order.dto.OrderBuilderContext;
import com.bbh.live.service.order.dto.OrderPaidInfo;
import com.bbh.live.service.order.dto.PreparedOrderDTO;
import com.bbh.live.service.user.UserInfoService;
import com.bbh.live.service.vipdeduction.GlobalVipDeductionLogService;
import com.bbh.live.service.vipdeduction.GlobalVipDeductionService;
import com.bbh.live.thread.ThreadPoolManager;
import com.bbh.live.thread.VirtualRunner;
import com.bbh.live.util.DepositUtils;
import com.bbh.model.*;
import com.bbh.secure.AuthUtil;
import com.bbh.service.deposit.DepositService;
import com.bbh.service.deposit.dto.UnFrozenDepositDTO;
import com.bbh.service.mq.enums.MqTopicEnum;
import com.bbh.service.mq.service.CoreMqService;
import com.bbh.service.pay.config.PayCenterProperties;
import com.bbh.util.AssertUtil;
import com.bbh.util.LogExUtil;
import com.bbh.util.SignUtil;
import com.bbh.vo.AuthUser;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 订单业务的Service
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderV2ServiceImpl implements OrderV2Service, RedisKey {

    private final CartService cartService;
    private final OrderChecker orderChecker;
    private final FenbeiCalculator fenbeiCalculator;
    private final OrderCalculator orderCalculator;
    private final UserInfoService userInfoService;
    private final OrderEntityBuilder orderEntityBuilder;
    private final GlobalOrderService globalOrderService;
    private final GlobalOrderSubService globalOrderSubService;
    private final GlobalOrderItemService globalOrderItemService;
    private final PayService payService;
    private final GlobalOrderPaymentMapper globalOrderPaymentMapper;
    private final PayCenterProperties payCenterProperties;
    private final DepositService depositService;
    private final GlobalOrderGoodsSnapshotService globalOrderGoodsSnapshotService;
    private final OrderCounter orderCounter;
    private final BuyerVipService buyerVipService;
    private final GlobalSeatMessageDataService globalSeatMessageDataService;
    private final LiveServiceProperties liveServiceProperties;
    private final IGlobalOrganizationService globalOrganizationService;
    private final GlobalFenbeiDetailService globalFenbeiDetailService;
    private final LiveGoodsService liveGoodsService;
    private final LiveRateTemplateService liveRateTemplateService;
    private final StringRedisTemplate stringRedisTemplate;
    private final MsgService msgService;
    private final GlobalOrgSeatService globalOrgSeatService;
    private final VipBuyerCardService vipBuyerCardService;
    private final LiveGoodsMapper liveGoodsMapper;
    private final GlobalOrderPaymentService globalOrderPaymentService;
    private final LiveGoodsCancelApplyMapper liveGoodsCancelApplyMapper;
    private final GlobalOrderQualityInspectService globalOrderQualityInspectService;
    private final GlobalVipDeductionService globalVipDeductionService;
    private final GlobalVipDeductionLogService globalVipDeductionLogService;

    @Lazy
    @Resource
    private CoreMqService coreMqService;

    /**
     * 准备订单信息
     *
     * @param prepareOrderDTO 勾选的商品id
     * @return 返回准备页面需要的数据
     */
    @Override
    public PrepareOrderVO prepareOrderInfoV2(PrepareOrderDTO prepareOrderDTO) {
        // 检查商品及订单状态
        orderChecker.checkAllStatus(prepareOrderDTO.getLiveGoodsIdList());

        // 通过商品id集合查询购物车列表
        QueryShoppingCartDTO query = new QueryShoppingCartDTO();
        query.setBizGoodsIdList(prepareOrderDTO.getLiveGoodsIdList());
        List<LiveShoppingCartDTO> shoppingCartList = liveGoodsMapper.selectLiveShoppingCartList(query);
        shoppingCartList.forEach(x -> {
            // 计算每个商品的买手服务费
            x.setBuyerServiceAmount(orderCalculator.computeBuyerServiceAmount(x.getSellPrice(), x.getBuyerServiceRate()));
            // 计算通道费
            x.setChannelAmount(orderCalculator.computeChannelAmount(x.getSellPrice()));
        });

        // 累加成交金额和买手通道费
        BigDecimal totalGoodsPrice = shoppingCartList.stream().map(LiveShoppingCartDTO::getSellPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalBuyerServiceAmount = shoppingCartList.stream().map(LiveShoppingCartDTO::getBuyerServiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 应支付金额 = 成交金额 + 买手通道费
        BigDecimal totalNeedPayAmount = totalGoodsPrice.add(totalBuyerServiceAmount);
        // 分组后的购物车列表
        List<LiveShoppingCartGroupVO> groupList = cartService.groupingShoppingCart(shoppingCartList);

        PrepareOrderVO prepareOrderVO = new PrepareOrderVO();

        // 按场次分组
        prepareOrderVO.setGroupList(groupList);
        // 商品金额
        prepareOrderVO.setTotalGoodsPrice(totalGoodsPrice);
        // 服务费
        prepareOrderVO.setTotalServiceAmount(totalBuyerServiceAmount);
        // 应支付金额
        prepareOrderVO.setTotalNeedPayAmount(totalNeedPayAmount);
        // 总计通道费
        prepareOrderVO.setTotalChannelAmount(shoppingCartList.stream().map(LiveShoppingCartDTO::getChannelAmount).reduce(BigDecimal.ZERO, BigDecimal::add));

        // 会员相关
        AggregatedUserDTO currentUser = userInfoService.current();
        GlobalOrganization organization = currentUser.getOrganization();
        prepareOrderVO.setIfAnnualFeeVip(Boolean.TRUE.equals(currentUser.getBuyerVip().getIsAnnualFeeVip()));

        prepareOrderVO.setFenbei(organization.getFenbei());
        prepareOrderVO.setIfVip(currentUser.getBuyerVip().getBuyerVipType() == BuyerVipTypeEnum.VIP);
        prepareOrderVO.setVipLevel(currentUser.getBuyerVip().getVipLevel());

        // 线上支付的分贝抵扣相关
        FenbeiCalculator.OnlineResult onlineResult = fenbeiCalculator.calculateOnlineDeductionFromCart(shoppingCartList, currentUser);
        if (onlineResult.getVipDeductionId() > 0L) {
            prepareOrderVO.setVipDeductionId(onlineResult.getVipDeductionId());
            prepareOrderVO.setVipDeduction(onlineResult.getVipDeduction());
            prepareOrderVO.setUsedDeductionVipCount(onlineResult.getUsedVipDeduction());
            prepareOrderVO.setCanDeductionVipCount(onlineResult.getVipDeduction().compareTo(prepareOrderVO.getTotalChannelAmount()) > 0L ? prepareOrderVO.getTotalChannelAmount() : onlineResult.getVipDeduction());
        }

        if (onlineResult.getVipDeductionId() == 0L) {
            // 分贝抵扣数量
            prepareOrderVO.setDeductionFenbeiCount(onlineResult.getTotalFenbeiDeductionCount());
            // 分贝抵扣金额
            prepareOrderVO.setDeductionFenbeiAmount(onlineResult.getTotalFenbeiDeductionAmount());
            // 超额抵扣金额
            prepareOrderVO.setOverDeductionFenbeiAmount(onlineResult.getOverDeductionFenbeiAmount());
            // 会员最多可抵扣的金额
            prepareOrderVO.setVipMaxDeductionFenbeiAmount(onlineResult.getVipMaxFenbeiAmount());
            // 会员最多可抵扣的次数
            prepareOrderVO.setVipMaxDeductionFenbeiCount(onlineResult.getVipMaxFenbeiCount());
        }
        // 是否可以分贝抵扣
        prepareOrderVO.setCanDeductionFenbei(onlineResult.getCanDeductionFenbei());

        // 银行转账的分贝抵扣相关
        FenbeiCalculator.OfflineResult offlineResult = fenbeiCalculator.calculateOfflineDeductionFromCart(shoppingCartList, currentUser);
        // 当前用户要扣除的数量
        prepareOrderVO.setOfflinePayDeductFenbeiPerMatch(offlineResult.getDeductFenbeiPerMatch());
        // 开通会员后要扣除的数量
        prepareOrderVO.setOfflinePayVipDeductFenbeiPerMatch(offlineResult.getVipDeductFenbeiPerMatch());
        // 普通用户要扣除的数量
        prepareOrderVO.setOfflinePayDefaultDeductFenbeiPerMatch(offlineResult.getDefaultDeductFenbeiPerMatch());
        // 本次要扣除的场次数量
        prepareOrderVO.setOfflinePayMatchCount(offlineResult.getMatchCount());
        // 本次要扣除的
        prepareOrderVO.setOfflinePayTotalDeductFenbei(offlineResult.getTotalDeductFenbei());

        return prepareOrderVO;
    }

    /**
     * 创建订单，返回调起支付参数
     *
     * @param createOrderV2DTO 创建订单参数
     * @return 调起支付参数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CreateOrderV2VO createOrderV2(CreateOrderV2DTO createOrderV2DTO) {
        OrderBuilderContext context = new OrderBuilderContext();

        initOrderBuilderContext(createOrderV2DTO, context);

        orderChecker.checkAllStatusAndUpdateContext(createOrderV2DTO.getLiveGoodsIdList(), context);

        // 构建订单实体类
        PreparedOrderDTO prepareOrderDTO = orderEntityBuilder.buildOrderResult(context);

        // 保存数据
        saveOrder(prepareOrderDTO, context);

        // 扣减分贝，包含线上和线下的处理
        subtractOrgFenbei(prepareOrderDTO, context);

        // 启动超时检测的延迟队列
        // 现在直播不需要自动关闭了，待付款订单一直保持
        // startTimeoutConsumer(prepareOrderDTO, context);

        // 如果有取消审核的申请，要自动关闭掉
        autoRejectOrderCancelApply(prepareOrderDTO.getOrderItemList());

        // 保存商品快照
        globalOrderGoodsSnapshotService.saveGoodsSnapshot(prepareOrderDTO.getGlobalOrder());

        // 返回
        CreateOrderV2VO vo = new CreateOrderV2VO();
        vo.setOrderNo(context.getOrderNo());
        vo.setGlobalOrderId(context.getGlobalOrderId());
        return vo;
    }

    /*-------------------------------------------------------------------------------*/

    // 如果有取消审核的申请，要自动关闭掉
    private void autoRejectOrderCancelApply(List<GlobalOrderItem> orderItemList) {
        if (CollUtil.isEmpty(orderItemList)) {
            return;
        }

        // 提取出商品id列表
        List<Long> bizGoodsIdList = orderItemList.stream().map(GlobalOrderItem::getBizGoodsId).distinct().collect(Collectors.toList());

        // 查询这些商品中，有审核中的取消申请的商品
        List<LiveGoods> matchGoodsList = liveGoodsService.lambdaQuery().in(LiveGoods::getId, bizGoodsIdList).eq(LiveGoods::getCancelStatus, LiveGoodsCancelStatusEnum.WAIT_VERIFY).list();
        if (CollUtil.isEmpty(matchGoodsList)) {
            return;
        }
        List<Long> matchGoodsIdList = matchGoodsList.stream().map(LiveGoods::getId).collect(Collectors.toList());

        // 批量更新商品的取消申请审核状态
        liveGoodsService.lambdaUpdate().set(LiveGoods::getCancelStatus, LiveGoodsCancelStatusEnum.REJECTED).in(LiveGoods::getId, matchGoodsIdList).update();

        // 批量更新取消申请记录的审核状态
        liveGoodsCancelApplyMapper.update(Wrappers.lambdaUpdate(LiveGoodsCancelApply.class).set(LiveGoodsCancelApply::getApprovalStatus, 20).set(LiveGoodsCancelApply::getAgreeDesc, "用户生成订单").set(LiveGoodsCancelApply::getVerifyAt, new Date()).in(LiveGoodsCancelApply::getBizGoodsId, matchGoodsIdList).eq(LiveGoodsCancelApply::getBizType, GlobalBizTypeEnum.LIVE).eq(LiveGoodsCancelApply::getApprovalStatus, 0));
    }

    /**
     * 扣减商户上的分贝
     *
     * @param preparedDTO 创建订单的综合参数
     * @param context     已经实例化的上下文
     */
    private void subtractOrgFenbei(PreparedOrderDTO preparedDTO, OrderBuilderContext context) {
        // 线下支付，要进行扣减，如果后续退回收银台，还要补回去
        if (context.getPayType() == GlobalPayTypeEnum.OFFLINE) {
            FenbeiCalculator.OfflineResult offlineResult = preparedDTO.getOfflineResult();
            if (offlineResult == null || offlineResult.getTotalDeductFenbei() <= 0) {
                return;
            }
            Integer orgFenbeiNumber = globalOrganizationService.getOrgFenbeiNumber(preparedDTO.getUser().getOrgId());
            int totalDeductFenbei = (int) offlineResult.getTotalDeductFenbei();
            if (NumberUtil.compare(orgFenbeiNumber, totalDeductFenbei) < 0) {
                throw new ServiceException("分贝余额不足");
            }
            globalFenbeiDetailService.saveTransferFenbeiDetail(totalDeductFenbei, orgFenbeiNumber, preparedDTO.getGlobalOrder(), context.getBuyerUser().getBuyerVip());
            // 先扣减分贝，如果后面退回收银台，要还回去
            globalOrganizationService.update(Wrappers.lambdaUpdate(GlobalOrganization.class).eq(GlobalOrganization::getId, preparedDTO.getUser().getOrgId()).setDecrBy(GlobalOrganization::getFenbei, offlineResult.getTotalDeductFenbei()));
            // 如果是会员用户，还得累加会员的线下抵扣分贝总额
            if (context.getBuyerUser().getBuyerVip() != null && context.getBuyerUser().getBuyerVip().getId() != null && context.getBuyerUser().getBuyerVip().getIsVip()) {
                LambdaUpdateWrapper<VipBuyerCard> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(VipBuyerCard::getId, context.getBuyerUser().getBuyerVip().getId()).setIncrBy(VipBuyerCard::getOfflineFbDeductNum, offlineResult.getTotalDeductFenbei()).setIncrBy(VipBuyerCard::getOfflineFbDeductTimes, 1);
                vipBuyerCardService.update(updateWrapper);
            }
        }
        // 如果是支付方式是线上，走线上的逻辑
        else {
            // 没用分贝直接跳过
            if (!context.getIfUseFenbei()) {
                return;
            }
            FenbeiCalculator.OnlineResult onlineResult = preparedDTO.getOnlineResult();
            if (onlineResult == null || onlineResult.getActualDeductFenbeiCount() <= 0) {
                return;
            }
            BigDecimal totalChannelAmount = context.getTotalChannelAmount();

            Long vipDeductionId = context.getVipDeductionId();
            if (vipDeductionId > 0L) {
                GlobalVipDeduction newDeduction = globalVipDeductionService.getById(vipDeductionId);
                BigDecimal subVipNumber = NumberUtil.sub(newDeduction.getVipDeduction(), newDeduction.getUsedVipDeduction());
                globalFenbeiDetailService.saveTransferVipDetail(totalChannelAmount, subVipNumber, preparedDTO.getGlobalOrder(), context, vipDeductionId);
            } else if (vipDeductionId == 0L) {
                // 分贝明细
                globalFenbeiDetailService.savePayDeductFenbeiDetail(preparedDTO.getOrderItemList(), globalOrganizationService.getOrgFenbeiNumber(preparedDTO.getUser().getOrgId()));
                // 扣减分贝
                globalOrganizationService.update(Wrappers.lambdaUpdate(GlobalOrganization.class).eq(GlobalOrganization::getId, preparedDTO.getUser().getOrgId()).setDecrBy(GlobalOrganization::getFenbei, onlineResult.getActualDeductFenbeiCount()));
            }
        }
    }

    /**
     * 保存支付信息
     *
     * @param context          订单上下文
     * @param preparedOrderDTO 准备好的订单数据
     */
    private void saveOrderPayment(PreparedOrderDTO preparedOrderDTO, OrderBuilderContext context) {
        GlobalOrder globalOrder = preparedOrderDTO.getGlobalOrder();

        GlobalOrderPayment globalOrderPayment = new GlobalOrderPayment();
        globalOrderPayment.setGlobalOrderId(globalOrder.getId());
        globalOrderPayment.setOutTradeNo(context.getOrderNo());
        globalOrderPayment.setPayStatus(GlobalOrderPaymentStatusEnum.WAIT);
        globalOrderPayment.setNeedPayAmount(globalOrder.getNeedPayAmount());
        globalOrderPayment.setRealPayedAmount(BigDecimal.ZERO);
        // 通道固定为江南银行
        globalOrderPayment.setPayChannel(GlobalOrderPaymentChannelEnum.JN);
        globalOrderPayment.setPayType(context.getPayType());
        globalOrderPaymentMapper.insert(globalOrderPayment);
    }

    /**
     * 启动支付超时监听
     */
    private void startTimeoutConsumer(PreparedOrderDTO prepareOrderDTO, OrderBuilderContext context) {
        // 使用订单号，而非支付流水号，方便消费时查询
        String orderNo = prepareOrderDTO.getGlobalOrder().getOrderNo();
        // 设置支付超时时间=最后支付时间+1分钟，与PHP保持一致
        Date lastPayAt = DateUtil.offsetMinute(prepareOrderDTO.getGlobalOrder().getLastPayAt(), 1);
        // 计算延迟时间，单位毫秒
        long delayMillis = DateUtil.between(lastPayAt, new Date(), DateUnit.MS);
        // 加入延迟队列
        coreMqService.send(MqTopicEnum.LIVE_PAY_CLOSE_DELAY, JSONUtil.toJsonStr(Dict.create().set("orderNo", orderNo).set("orderId", prepareOrderDTO.getGlobalOrder().getId())), delayMillis);
    }

    /**
     * 保存订单数据，并在上下文中写入流水号
     *
     * @param preparedOrder 准备好的各订单列表
     * @param context       上下文
     */
    @SuppressWarnings("all")
    private void saveOrder(PreparedOrderDTO preparedOrder, OrderBuilderContext context) {
        Long vipDeductionId = context.getVipDeductionId();
        GlobalOrder globalOrder = preparedOrder.getGlobalOrder();
        if (vipDeductionId > 0L && context.getIfUseFenbei()) {
            globalOrder.setFenbeiDeductionAmount(BigDecimal.ZERO);
            globalOrder.setFenbeiDeductionCount(0);
        }
        globalOrderService.save(globalOrder);
        Long orderId = preparedOrder.getGlobalOrder().getId();
        // 写入订单流水号和订单号
        context.setOrderNo(globalOrder.getOrderNo());
        LogExUtil.warnLog("插入 1 --订单流水号：{}", globalOrder.getOrderNo());
        context.setGlobalOrderId(orderId);

        // 批量录入order_sub，录入之后再依次更新order_item列表中满足条件的item的sub_id
        for (GlobalOrderSub orderSub : preparedOrder.getOrderSubList()) {
            orderSub.setOrderNo(preparedOrder.getGlobalOrder().getOrderNo());
            orderSub.setGlobalOrderId(orderId);
            if (vipDeductionId > 0L && context.getIfUseFenbei()) {
                orderSub.setChannelAmount(BigDecimal.ZERO);
                orderSub.setFenbeiDeductionAmount(BigDecimal.ZERO);
                orderSub.setFenbeiDeductionCount(0);
            }
        }
        globalOrderSubService.saveBatch(preparedOrder.getOrderSubList());

        // 批量录入order_item，需要填充sub订单的id
        BigDecimal totalChannelAmount = BigDecimal.ZERO;
        for (GlobalOrderItem orderItem : preparedOrder.getOrderItemList()) {
            totalChannelAmount = totalChannelAmount.add(orderItem.getChannelAmount());
            preparedOrder.getOrderSubList().stream().filter(x -> x.getSellerOrgId().equals(orderItem.getSellerOrgId())).findFirst().ifPresent(x -> orderItem.setGlobalOrderSubId(x.getId()));
            // 按商户取出所有的商品项，一个商户对应一个子订单
            orderItem.setGlobalOrderId(orderId);
            if (vipDeductionId > 0L && context.getIfUseFenbei()) {
                orderItem.setVipDeductionAmount(orderItem.getChannelAmount());
                orderItem.setChannelAmount(BigDecimal.ZERO);
                orderItem.setFenbeiDeductionAmount(BigDecimal.ZERO);
                orderItem.setFenbeiDeductionCount(0);
            }
        }
        context.setTotalChannelAmount(totalChannelAmount);
        globalOrderItemService.saveBatch(preparedOrder.getOrderItemList());
    }

    /**
     * 初始化订单构建的上下文
     *
     * @param dto     创建订单的入参
     * @param context 已经实例化的上下文
     */
    private void initOrderBuilderContext(CreateOrderV2DTO dto, OrderBuilderContext context) {
        context.setLiveGoodsIdList(dto.getLiveGoodsIdList());
        context.setIfUseFenbei(dto.getIfUseFenbei());
        context.setPayType(dto.getPayType());
        context.setReceiveAddressInfo(dto.getReceiveAddressInfo());
        context.setReceiveAddressId(dto.getReceiveAddressId());
        context.setBuyerUser(userInfoService.current());
        context.setWxOpenId(dto.getCode());
        context.setAliPayUserId(dto.getCode());
        context.setIfQualityInspect(dto.getIfQualityInspect());
        context.setVipDeductionId(dto.getVipDeductionId());
    }

    /**
     * 取消支付，把商品退回到直播的购物车
     *
     * @param cancelPayDTO 取消支付参数
     */
    @Transactional
    @SuppressWarnings("all")
    @Override
    public void cancelPay(CancelPayDTO cancelPayDTO) {
        AssertUtil.assertNotNull(cancelPayDTO.getOrderNo(), "订单流水号不能为空");
        String globalOrderNo = cancelPayDTO.getOrderNo();

        // 先查总订单，拿到总订单id
        GlobalOrder globalOrder = globalOrderService.getOne(Wrappers.lambdaQuery(GlobalOrder.class).eq(GlobalOrder::getOrderNo, globalOrderNo));
        AssertUtil.assertFalse(globalOrder == null, "订单不存在");

        // 检查订单状态是否待支付或待审核
        AssertUtil.assertTrue(globalOrder.getOrderStatus().equals(GlobalOrderStatusEnum.TO_BE_PAID) || globalOrder.getOrderStatus().equals(GlobalOrderStatusEnum.TRANSFER_AUDIT), "订单不是待支付或待审核状态");

        // 查询所有的商品，获取支付方式，并且在后面要退回购物车
        List<GlobalOrderItem> orderItemList = globalOrderItemService.list(Wrappers.lambdaQuery(GlobalOrderItem.class).eq(GlobalOrderItem::getGlobalOrderId, globalOrder.getId()));
        GlobalPayTypeEnum payType = orderItemList.getFirst().getPayType();

        // 如果是线下转账，要检查有没有转过钱
        if (payType == GlobalPayTypeEnum.OFFLINE) {
            checkOfflinePayment(globalOrder);
        }

        // 更新总订单状态=60
        globalOrderService.updateOrderStatus(globalOrder.getId(), GlobalOrderStatusEnum.CANCELED.getCode());

        // 先查询这笔订单的未支付的payment
        List<GlobalOrderPayment> orderPaymentList = globalOrderPaymentMapper.selectList(Wrappers.lambdaQuery(GlobalOrderPayment.class).eq(GlobalOrderPayment::getGlobalOrderId, globalOrder.getId()).eq(GlobalOrderPayment::getPayStatus, GlobalOrderPaymentStatusEnum.WAIT));
        if (CollUtil.isNotEmpty(orderPaymentList)) {
            // 如果有要调用中台的关闭
            try {
                for (GlobalOrderPayment payment : orderPaymentList) {
                    // 要用payment的trade_no，因为这个是独立于order_no的
                    payService.orderClosePay(payment.getOutTradeNo());
                }
            } catch (Exception e) {
                // 捕获异常，确保中台不影响付款业务
                LogExUtil.warnLog(globalOrderNo + " 关闭订单失败: " + e.getMessage());
            }

            // 关闭中台后，再去更新payment
            globalOrderPaymentMapper.update(Wrappers.lambdaUpdate(GlobalOrderPayment.class).in(GlobalOrderPayment::getId, orderPaymentList.stream().map(GlobalOrderPayment::getId).toList())
                    // 区分手动取消和自动取消
                    .set(GlobalOrderPayment::getPayStatus, cancelPayDTO.getIfAuto() ? GlobalOrderPaymentStatusEnum.CANCEL_MANUAL : GlobalOrderPaymentStatusEnum.CANCEL_AUTO));
        }

        // 状态更新完之后，删除订单和流水
        globalOrderService.removeById(globalOrder.getId());
        globalOrderSubService.remove(Wrappers.lambdaQuery(GlobalOrderSub.class).eq(GlobalOrderSub::getGlobalOrderId, globalOrder.getId()));
        globalOrderItemService.remove(Wrappers.lambdaQuery(GlobalOrderItem.class).eq(GlobalOrderItem::getGlobalOrderId, globalOrder.getId()));
        globalOrderPaymentMapper.delete(Wrappers.lambdaQuery(GlobalOrderPayment.class).eq(GlobalOrderPayment::getGlobalOrderId, globalOrder.getId()));

        // 归还扣减的分贝
        returnOrderFenbei(globalOrder, orderItemList);
    }

    // 检查线下转账是否转过钱
    private void checkOfflinePayment(GlobalOrder globalOrder) {
        String orderNo = globalOrder.getOrderNo();
        JSONObject jsonObject;
        try {
            Object result = payService.getOfflinePaidInfo(orderNo, globalOrder.getCreatedAt());
            jsonObject = JSONUtil.parseObj(result);
        } catch (Exception e) {
            LogExUtil.errorLog("退回收银台，查询线下转账是否转过钱，失败，orderNo:" + orderNo, e);
            return;
        }

        if (jsonObject == null || !Objects.equals(jsonObject.getStr("code"), "00000")) {
            return;
        }

        JSONObject entries = jsonObject.getJSONObject("result");
        BigDecimal amount = entries.getBigDecimal("acctAmt");
        if (amount.compareTo(BigDecimal.ZERO) != 0) {
            throw new ServiceException("您已在线下进行了转账，暂不支持手动取消");
        }
    }

    /**
     * 返还订单使用的分贝
     *
     * @param order 订单详情
     */
    public void returnOrderFenbei(GlobalOrder order, List<GlobalOrderItem> orderItemList) {
        // 这里要判断支付类型，分别走不同的逻辑
        GlobalPayTypeEnum payType = orderItemList.getFirst().getPayType();
        String orderNo = order.getOrderNo();
        GlobalVipDeductionLog deductionLog = globalVipDeductionLogService.getOne(Wrappers.lambdaQuery(GlobalVipDeductionLog.class).eq(GlobalVipDeductionLog::getPayOsn, orderNo));
        if (ObjUtil.isNotNull(deductionLog)) {
            BigDecimal deduction = deductionLog.getDeduction();
            Long vipDeductionId = deductionLog.getVipDeductionId();
            globalVipDeductionLogService.removeById(deductionLog.getId());
            globalVipDeductionService.update(Wrappers.lambdaUpdate(GlobalVipDeduction.class).eq(GlobalVipDeduction::getId, vipDeductionId).setDecrBy(GlobalVipDeduction::getUsedVipDeduction, deduction));
            return;
        }

        if (payType == GlobalPayTypeEnum.OFFLINE) {
            // 保存记录，并从历史记录中获取要返还的分贝
            FenbeiOfflineTransferRemarkDTO fenbeiResult = globalFenbeiDetailService.saveCancelPayOrderFenbeiDetail(globalOrganizationService.getOrgFenbeiNumber(order.getBuyerOrgId()), order);
            if (fenbeiResult.getChangeNumber() <= 0) {
                return;
            }

            // 返还分贝
            globalOrganizationService.update(Wrappers.lambdaUpdate(GlobalOrganization.class).eq(GlobalOrganization::getId, order.getBuyerOrgId()).setIncrBy(GlobalOrganization::getFenbei, fenbeiResult.getChangeNumber()));

            // 如果是会员用户，还得累加会员的线下抵扣分贝总额
            if (fenbeiResult.getIsVip()) {
                LambdaUpdateWrapper<VipBuyerCard> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(VipBuyerCard::getId, fenbeiResult.getVipId()).setDecrBy(VipBuyerCard::getOfflineFbDeductNum, fenbeiResult.getChangeNumber()).setDecrBy(VipBuyerCard::getOfflineFbDeductTimes, 1);
                vipBuyerCardService.update(updateWrapper);
            }
            return;
        }

        // 没用分贝，那直接中断
        if (order.getFenbeiDeductionCount() == 0) {
            return;
        }

        // 记录取消订单分贝明细
        globalFenbeiDetailService.saveCancelPayOrderFenbeiDetail(orderItemList, globalOrganizationService.getOrgFenbeiNumber(order.getBuyerOrgId()));

        // 用了就返还
        globalOrganizationService.update(Wrappers.lambdaUpdate(GlobalOrganization.class).eq(GlobalOrganization::getId, order.getBuyerOrgId()).setIncrBy(GlobalOrganization::getFenbei, order.getFenbeiDeductionCount()));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String paySuccessCallBack(SignDTO signDTO) {
        var sign = signDTO.getSign();
        if (!SignUtil.checkSign(sign, payCenterProperties.getSalt())) {
            LogExUtil.errorLog("支付回调签名校验失败,sign:" + sign, new ServiceException("支付回调签名校验失败"));
        }
        String json = Base64.decodeStr(sign, StandardCharsets.UTF_8);
        PaySuccessDTO paySuccessDTO = JSONUtil.toBean(json, PaySuccessDTO.class);
        // 支付处理
        paySuccess(paySuccessDTO.getOutTradeNo());
        return "success";
    }

    private void removeNotifyLock(Long globalOrderId) {
        stringRedisTemplate.delete("order:paySuccess:" + globalOrderId);
    }

    /**
     * 使用指数退避策略检查线上支付结果
     *
     * @param outTradeNo 订单流水号
     * @return 支付是否成功
     */
    private boolean checkOnlinePaymentWithRetry(String outTradeNo) {
        // 重试间隔数组，指数退避
        int[] retryDelays = {100, 500, 1000, 1800, 3000};

        for (int i = 0; i < retryDelays.length; i++) {
            try {
                // 等待指定时间
                Thread.sleep(retryDelays[i]);

                // 调用支付服务检查支付状态
                boolean paymentSuccess = payService.getOrderPaidInfoStatus(outTradeNo);
                if (paymentSuccess) {
                    LogExUtil.infoLog("线上支付查询成功，流水号: {}, 重试次数: {}", outTradeNo, i + 1);
                    return true;
                }

                LogExUtil.infoLog("线上支付查询未成功，流水号: {}, 重试次数: {}, 等待下次重试", outTradeNo, i + 1);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                LogExUtil.warnLog("线上支付查询被中断，流水号: " + outTradeNo, e);
                return false;
            } catch (Exception e) {
                LogExUtil.warnLog("线上支付查询异常，流水号: {}, 重试次数: {}, 异常: {}", outTradeNo, i + 1, e);
            }
        }

        LogExUtil.infoLog("线上支付查询失败，已达到最大重试次数，流水号: {}", outTradeNo);
        return false;
    }

    @SuppressWarnings("all")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void paySuccess(String outTradeNo) {
        LogExUtil.infoLog("=============> paySuccess开始处理，流水号: {} <=============", outTradeNo);
        // 校验支付流水、订单是否存在
        var paySuccessContext = orderChecker.getPaySuccessContext(outTradeNo);
        // 订单信息
        GlobalOrder globalOrder = paySuccessContext.getOrder();
        LogExUtil.infoLog("支付成功回调处理，订单号: {}, 订单状态: {}", globalOrder.getOrderNo(), globalOrder.getOrderStatus());
        stringRedisTemplate.opsForValue().set("order:paySuccess:" + globalOrder.getId(), "1", 3, TimeUnit.SECONDS);
        // 支付信息
        GlobalOrderPayment orderPayment = paySuccessContext.getOrderPayment();

        OrderPaidInfo orderPaidInfo;
        if (liveServiceProperties.getEnableMockPay()) {
            // 模拟支付，只需要创建参数
            orderPaidInfo = new OrderPaidInfo();
            orderPaidInfo.setLastPaymentId(orderPayment.getId());
            orderPaidInfo.setTransAmt(globalOrder.getNeedPayAmount().toString());
            orderPaidInfo.setPayAt(new Date());
        } else {
            // 回查支付中台支付状态 校验支付是否成功
            if (orderPayment.getPayType() == GlobalPayTypeEnum.OFFLINE) {
                // 线下支付的回查
                Object result = payService.getOfflinePaidInfo(outTradeNo, globalOrder.getCreatedAt());
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if (!Objects.equals(jsonObject.getStr("code"), "00000")) {
                    // 通知线下转账审核失败
                    msgService.offlineTransferFailed(globalOrder.getId(), globalOrder.getBuyerOrgId(), globalOrder.getBuyerSeatId());
                    throw new ServiceException("线下转账回查失败");
                }
                JSONObject entries = jsonObject.getJSONObject("result");
                BigDecimal amount = entries.getBigDecimal("acctAmt");
                Date payAt = DateUtil.parseDateTime(entries.getStr("payTime"));
                // 如果返回的金额小于应支付金额，要发送转账审核失败的通知
                if (amount.compareTo(globalOrder.getNeedPayAmount()) < 0) {
                    // 通知线下转账审核失败
                    msgService.offlineTransferFailed(globalOrder.getId(), globalOrder.getBuyerOrgId(), globalOrder.getBuyerSeatId());
                    throw new ServiceException("线下转账回查失败");
                }

                orderPaidInfo = new OrderPaidInfo();
                orderPaidInfo.setLastPaymentId(orderPayment.getId());
                orderPaidInfo.setTransAmt(globalOrder.getNeedPayAmount().toString());
                orderPaidInfo.setPayAt(payAt);
            } else {
                Date now = new Date();
                // 线上支付，使用指数退避策略重试查询支付结果
                boolean paymentSuccess = checkOnlinePaymentWithRetry(outTradeNo);
                AssertUtil.assertTrue(paymentSuccess, "订单未支付");
                orderPaidInfo = new OrderPaidInfo();
                orderPaidInfo.setLastPaymentId(orderPayment.getId());
                orderPaidInfo.setTransAmt(globalOrder.getNeedPayAmount().toString());
                orderPaidInfo.setPayAt(now);
            }
        }

        // 修改订单
        this.orderPaid(globalOrder, orderPaidInfo, outTradeNo);
        LogExUtil.infoLog("订单支付成功处理完成，订单号: {}, 流水号: {}", globalOrder.getOrderNo(), outTradeNo);

        // 修改支付流水
        LambdaUpdateWrapper<GlobalOrderPayment> paymentUpdate = new LambdaUpdateWrapper<>();
        paymentUpdate.set(GlobalOrderPayment::getPayStatus, GlobalOrderPaymentStatusEnum.PAID);
        paymentUpdate.set(GlobalOrderPayment::getRealPayedAmount, orderPaidInfo.getTransAmt());
        paymentUpdate.set(GlobalOrderPayment::getPayAt, orderPaidInfo.getPayAt());
        paymentUpdate.eq(GlobalOrderPayment::getId, orderPayment.getId());
        globalOrderPaymentMapper.update(paymentUpdate);
        LogExUtil.infoLog("支付流水更新完成，订单号: {}, 流水号: {}", globalOrder.getOrderNo(), outTradeNo);

        // 退还已支付的保证金
        returnDeposit(globalOrder);
        LogExUtil.infoLog("=============> 解冻保证金，流水号:" + outTradeNo + " <=============");

        // 会员加经验值
        buyerVipService.increaseExpAfterPaySuccess(globalOrder.getOrderNo());
        LogExUtil.infoLog("=============> 会员加经验值，流水号:" + outTradeNo + " <=============");

        // 提醒商家发货
        msgService.deliveryOrderGoods(globalOrderService.getOrderSubList(globalOrder.getId()));

        // 解锁
        removeNotifyLock(globalOrder.getId());
    }

    /**
     * 处理订单支付成功后的业务逻辑
     * 注：默认使用PROPAGATION_REQUIRED传播行为，会加入到调用方的事务中，确保数据一致性
     *
     * @param globalOrder 订单信息
     * @param orderPaidInfo 支付信息
     * @param actualOrderNo 实际完成支付的订单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void orderPaid(GlobalOrder globalOrder, OrderPaidInfo orderPaidInfo, String actualOrderNo) {
        Long globalOrderId = globalOrder.getId();
        LogExUtil.infoLog("开始处理订单支付成功业务，订单号: {}, 实际支付订单号: {}", globalOrder.getOrderNo(), actualOrderNo);

        // 用于批量更新的数据列表
        List<GlobalOrderItem> orderItemUpdateList = new ArrayList<>();
        List<GlobalOrderSub> orderSubUpdateList = new ArrayList<>();

        // 查询item列表
        List<GlobalOrderItem> orderItemList = globalOrderItemService.list(Wrappers.lambdaQuery(GlobalOrderItem.class).eq(GlobalOrderItem::getGlobalOrderId, globalOrderId));
        // 商品清单id
        var liveGoodsIdList = orderItemList.stream().map(GlobalOrderItem::getBizGoodsId).distinct().toList();
        // 直播间id
        var liveRoomIdList = orderItemList.stream().map(GlobalOrderItem::getBizId).distinct().toList();

        List<GlobalOrderItem> orderFilterItemList = orderItemList.stream().filter(item -> item.getIfInspect() == 1).toList();

        // 查询涉及的classify表
        LiveGoodsQueryReq query = new LiveGoodsQueryReq();
        query.setLiveGoodsIdList(liveGoodsIdList).setCurrentPage(1);
        query.setPerPage(-1);
        IPage<LiveGoodsDTO> liveGoodsList = liveGoodsService.getLiveGoodsList(query);
        List<LiveGoodsDTO> goodsListWithClassify = Objects.requireNonNullElse(liveGoodsList.getRecords(), new ArrayList<>());

        // 查询出各个直播间绑定的模板费率列表，方便后面匹配计算
        var rateTemplateList = liveRateTemplateService.getRoomRateTemplateList(liveRoomIdList);

        // 计算每个商品的卖家服务费
        for (GlobalOrderItem orderItem : orderItemList) {
            // 匹配到模板费率
            var itemList = rateTemplateList.stream().filter(template -> template.getRoomId().equals(orderItem.getBizId())).findFirst().orElse(new RoomRateTemplateDTO()).getItemList();
            // 现在从pt_goods表获取分类id
            var oneClassifyId = orderCalculator.matchPlatformClassifyId(orderItem, goodsListWithClassify);
            // 要用商品售价去匹配费率
            var sellerServiceRate = orderCalculator.matchSellerServiceRate(itemList, orderItem.getGoodsPrice(), oneClassifyId);
            // 计算卖家服务费，也要用商品售价去计算
            var sellerServiceAmount = orderCalculator.computeSellerServiceAmount(orderItem.getGoodsPrice(), sellerServiceRate);

            // 标记更新
            GlobalOrderItem update = new GlobalOrderItem();
            update.setId(orderItem.getId());
            update.setGlobalOrderSubId(orderItem.getGlobalOrderSubId());
            update.setServiceAmount(Objects.requireNonNullElse(sellerServiceAmount, BigDecimal.ZERO));
            update.setServiceRate(Objects.requireNonNullElse(sellerServiceRate, BigDecimal.ZERO));
            update.setRealPayedAmount(orderItem.getNeedPayAmount());
            update.setOrderPayAt(orderPaidInfo.getPayAt());
            update.setOrderStatus(GlobalOrderStatusEnum.TO_BE_DELIVERED);
            orderItemUpdateList.add(update);
        }
        globalOrderItemService.updateBatchById(orderItemUpdateList);

        // 2025-03-25 添加质检
        if (CollUtil.isNotEmpty(orderFilterItemList)) {
            List<GlobalOrderQualityInspect> addList = new ArrayList<>();
            for (GlobalOrderItem orderItem : orderFilterItemList) {
                DateTime date = DateUtil.date();
                GlobalOrderQualityInspect addEntity = new GlobalOrderQualityInspect();
                addEntity.setSubOrderId(orderItem.getGlobalOrderSubId());
                addEntity.setItemId(orderItem.getId());
                addEntity.setBizId(orderItem.getBizId());
                addEntity.setBizGoodsId(orderItem.getBizGoodsId());
                addEntity.setBizType(orderItem.getBizType());
                addEntity.setCreatedAt(date);
                addEntity.setUpdatedAt(date);
                addList.add(addEntity);
            }
            if (CollUtil.isNotEmpty(addList)) {
                globalOrderQualityInspectService.saveBatch(addList);
            }
        }

        // 查询商户订单，汇总卖家服务费
        List<GlobalOrderSub> orderSubList = globalOrderSubService.list(Wrappers.lambdaQuery(GlobalOrderSub.class).eq(GlobalOrderSub::getGlobalOrderId, globalOrderId));
        for (GlobalOrderSub orderSub : orderSubList) {
            GlobalOrderSub update = new GlobalOrderSub();
            update.setId(orderSub.getId());
            update.setGlobalOrderId(globalOrderId);
            // 累加所有商品的服务费
            update.setServiceAmount(orderItemUpdateList.stream().filter(item -> item.getGlobalOrderSubId().equals(orderSub.getId())).map(GlobalOrderItem::getServiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            // 服务费率直接给零：可能是多个直播间汇总到一条sub数据，无法判定取哪个直播间的费率
            update.setServiceRate(BigDecimal.ZERO);
            update.setOrderStatus(GlobalOrderStatusEnum.TO_BE_DELIVERED);
            // 实际支付金额
            update.setRealPayedAmount(orderSub.getNeedPayAmount());
            // 使用实际完成支付的订单号更新order_no，避免调起多次支付后，用支付宝手动付前面的订单
            update.setOrderNo(actualOrderNo);
            orderSubUpdateList.add(update);
        }
        globalOrderSubService.updateBatchById(orderSubUpdateList);

        // 更新总订单 状态，实付金额，使用实际完成支付的订单号
        LambdaUpdateWrapper<GlobalOrder> update = new LambdaUpdateWrapper<>();
        update.set(GlobalOrder::getOrderStatus, GlobalOrderStatusEnum.TO_BE_DELIVERED)
              .set(GlobalOrder::getRealPayedAmount, orderPaidInfo.getTransAmt())
              .set(GlobalOrder::getLastPaymentId, orderPaidInfo.getLastPaymentId())
              .set(GlobalOrder::getOrderNo, actualOrderNo)
              .eq(GlobalOrder::getId, globalOrderId);
        globalOrderService.update(update);
        LogExUtil.infoLog("总订单更新完成，订单号: {}, 实际支付订单号: {}", globalOrder.getOrderNo(), actualOrderNo);
    }

    /**
     * 模拟支付成功回调，给测试环境使用
     *
     * @param orderNo 订单号
     * @return
     */
    @SuppressWarnings("all")
    @Transactional
    public void mockPaySuccess(String orderNo) {
        // 先创建支付流水
        PayDTO payDTO = new PayDTO();
        payDTO.setPayType(2);
        payDTO.setOrderNo(orderNo);
        payDTO.setCode("mock");
        var payResult = pay(payDTO);
        if (payResult != null) {
            var outTradeNo = ((Dict) payResult).getStr("tradeNo");
            // 再走回调
            paySuccess(outTradeNo);
        }
    }

    @SuppressWarnings("all")
    private void returnDeposit(GlobalOrder globalOrder) {
        var userInfoDTO = DepositUtils.getUserInfoDTO(globalOrder.getBuyerOrgId());
        var orderItems = globalOrderService.getOrderItems(globalOrder.getId(), GlobalOrderItem::getId, GlobalOrderItem::getBizGoodsId);

        // 退还保证金
        UnFrozenDepositDTO dto = new UnFrozenDepositDTO();
        orderItems.forEach(orderItem -> {
            dto.setGoodsInfo(DepositUtils.getGoodsInfoDTO(orderItem.getBizGoodsId()));
            dto.setSuccessInfo(userInfoDTO);
            dto.setCreateId(globalOrder.getBuyerSeatId());
            dto.setCreateName(globalOrgSeatService.getSeatName(globalOrder.getBuyerSeatId()));
            depositService.unFrozenDeposit(dto);
        });
    }

    /**
     * 单独的调起支付接口，仅用于线上支付
     *
     * @param payDTO 流水号
     * @return 调起支付参数
     */
    @SuppressWarnings("all")
    @Transactional
    @Override
    public Object pay(PayDTO payDTO) {
        AssertUtil.assertNotNull(payDTO.getOrderNo(), "订单流水号不能为空");
        AssertUtil.assertNotNull(payDTO.getPayType(), "支付方式不能为空");
        if (payDTO.getPayType() != 3) {
            AssertUtil.assertNotNull(payDTO.getCode(), "code不能为空");
        }

        String globalOrderNo = payDTO.getOrderNo();
        LogExUtil.infoLog("=============> 调起支付，流水号:" + globalOrderNo + " <=============");
        GlobalPayTypeEnum payType = payDTO.getPayType() == 1 ? GlobalPayTypeEnum.WECHAT : payDTO.getPayType() == 2 ? GlobalPayTypeEnum.ALIPAY : GlobalPayTypeEnum.OFFLINE;

        // 根据流水号查询到订单
        GlobalOrder globalOrder = globalOrderService.getOne(Wrappers.lambdaQuery(GlobalOrder.class).eq(GlobalOrder::getOrderNo, globalOrderNo));
        AssertUtil.assertFalse(globalOrder == null, "订单不存在");

        // 同一个订单5秒内只能调起一次
        String cacheLockKey = stringRedisTemplate.opsForValue().get("order:pay:" + globalOrder.getId());
        if (cacheLockKey != null) {
            throw new ServiceException("您的操作太快啦，请稍后再试");
        }
        stringRedisTemplate.opsForValue().set("order:pay:" + globalOrder.getId(), "1", 5, TimeUnit.SECONDS);

        // 如果已经进了支付回调，则不再调起支付
        String notifyLockKey = stringRedisTemplate.opsForValue().get("order:paySuccess:" + globalOrder.getId());
        if (notifyLockKey != null) {
            throw new ServiceException("订单正在处理中，请稍后再试");
        }

        // 检查订单状态是否待支付
        AssertUtil.assertTrue(globalOrder.getOrderStatus().equals(GlobalOrderStatusEnum.TO_BE_PAID), "订单不是待支付状态");
        Long globalOrderId = globalOrder.getId();

        // 查payment表，看有没有未支付记录，如果有要调用中台的关闭
        List<GlobalOrderPayment> orderPaymentList = globalOrderPaymentMapper.selectList(Wrappers.lambdaQuery(GlobalOrderPayment.class).eq(GlobalOrderPayment::getGlobalOrderId, globalOrderId).eq(GlobalOrderPayment::getPayStatus, GlobalOrderPaymentStatusEnum.WAIT));
        if (CollUtil.isNotEmpty(orderPaymentList)) {
            // 如果有，先查一下支付结果，如果是已经支付了，就直接走支付成功的流程，同时throw提示“已支付”
            boolean paySuccess = false;
            try {
                paySuccess = checkPaySuccess(globalOrderId);
            } catch (Exception e) {
                LogExUtil.infoLog("检查支付结果: {}", e, e.getMessage());
            }
            if (paySuccess) {
                throw new ServiceException("已完成支付，请返回查看订单");
            }

            ThreadPoolManager.getGlobalBizExecutor().execute(() -> {
                // 如果是未支付，要调用中台的关闭
                try {
                    for (GlobalOrderPayment payment : orderPaymentList) {
                        // 要用payment的trade_no，因为这个是独立于order_no的
                        payService.orderClosePay(payment.getOutTradeNo());
                    }
                } catch (Exception e) {
                    // 捕获异常，确保中台不影响付款业务
                    LogExUtil.infoLog(globalOrderNo + " 关闭订单失败: " + e.getMessage());
                }
            });

            // 关闭之后，更新这笔订单的payment的状态为自动取消
            globalOrderPaymentMapper.update(Wrappers.lambdaUpdate(GlobalOrderPayment.class).in(GlobalOrderPayment::getId, orderPaymentList.stream().map(GlobalOrderPayment::getId).toList()).set(GlobalOrderPayment::getPayStatus, GlobalOrderPaymentStatusEnum.CANCEL_AUTO));
        }

        // 生成新的流水号
        String newTradeNo = orderEntityBuilder.buildOrderNo(false);

        // 保存新的payment
        GlobalOrderPayment globalOrderPayment = new GlobalOrderPayment();
        globalOrderPayment.setGlobalOrderId(globalOrderId);
        globalOrderPayment.setOutTradeNo(newTradeNo);
        globalOrderPayment.setPayStatus(GlobalOrderPaymentStatusEnum.WAIT);
        globalOrderPayment.setNeedPayAmount(globalOrder.getNeedPayAmount());
        globalOrderPayment.setRealPayedAmount(BigDecimal.ZERO);
        // 通道固定为江南银行
        globalOrderPayment.setPayChannel(GlobalOrderPaymentChannelEnum.JN);
        globalOrderPayment.setPayType(payType);
        // 用订单创建人作为付款人
        globalOrderPayment.setCreateUserId(globalOrder.getCreateUserId());
        globalOrderPayment.setCreateSeatId(globalOrder.getCreateSeatId());
        globalOrderPaymentMapper.insert(globalOrderPayment);

        // 更新订单表，确保流水号一致，如果流水号不一致会导致财务对账对不上
        globalOrderService.lambdaUpdate().set(GlobalOrder::getOrderNo, newTradeNo).eq(GlobalOrder::getId, globalOrderId).update();
        // 记录原始流水号到新流水号的日志
        LogExUtil.infoLog("原始流水号:" + globalOrderNo + ",新流水号:" + newTradeNo);

        // 每次生成新的子订单流水号
        List<GlobalOrderSub> globalOrderSubList = globalOrderSubService.list(Wrappers.lambdaQuery(GlobalOrderSub.class).eq(GlobalOrderSub::getGlobalOrderId, globalOrderId));
        for (GlobalOrderSub orderSub : globalOrderSubList) {
            String subOrderNo = orderEntityBuilder.buildOrderNo(true);
            orderSub.setOrderSubNo(subOrderNo);
            // 更新子订单表
            globalOrderSubService.lambdaUpdate()
                    // 更新子订单表中的OrderNo，确保流水号一致
                    .set(GlobalOrderSub::getOrderNo, newTradeNo)
                    // 子订单流水号也要更新
                    .set(GlobalOrderSub::getOrderSubNo, subOrderNo).eq(GlobalOrderSub::getId, orderSub.getId()).update();
        }

        // 更新 vip 抵扣 订单 号
        globalVipDeductionLogService.lambdaUpdate().set(GlobalVipDeductionLog::getPayOsn, newTradeNo).eq(GlobalVipDeductionLog::getPayOsn, globalOrderNo).update();

        // 用新的流水号调用中台的支付接口
        PreparedOrderDTO preparedOrderDTO = new PreparedOrderDTO();
        preparedOrderDTO.setGlobalOrder(globalOrder);
        preparedOrderDTO.setOrderSubList(globalOrderSubList);
        OrderBuilderContext context = new OrderBuilderContext().setPaymentTradeNo(newTradeNo).setPayType(payType).setAliPayUserId(payDTO.getCode()).setWxOpenId(payDTO.getCode());

        // 判断是否为线下支付
        if (payType == GlobalPayTypeEnum.OFFLINE) {
            // 线下转账，获取转账信息
            return handleOfflinePay(context, preparedOrderDTO, globalOrder, globalOrderPayment, globalOrderId);
        } else {
            // 线上支付mock模式直接返回
            if (liveServiceProperties.getEnableMockPay()) {
                return mockPaymentResult(payType, globalOrder, globalOrderId, newTradeNo);
            }
            // 线上支付直接返回支付参数
            return payService.orderPay(context, preparedOrderDTO);
        }
    }

    /**
     * 线下转账，获取转账信息
     */
    private OfflinePayResultDTO handleOfflinePay(OrderBuilderContext context, PreparedOrderDTO preparedOrderDTO, GlobalOrder globalOrder, GlobalOrderPayment globalOrderPayment, Long globalOrderId) {
        // 调用支付服务获取账户信息
        Object payResult = null;
        try {
            if (liveServiceProperties.getEnableMockPay()) {
                JSONObject mockResult = new JSONObject();
                mockResult.set("code", "00000");
                mockResult.set("result", new JSONObject().set("acctName", "模拟收款人").set("bankNo", "6222000000000000000").set("bankName", "模拟银行").set("outTradeNo", null).set("orderAcctNo", "**********"));
                payResult = mockResult;
            } else {
                payResult = payService.orderPay(context, preparedOrderDTO);
            }
        } catch (Exception e) {
            LogExUtil.errorLog("调用中台线下转账下单失败", e);
            throw new ServiceException("调用中台线下转账下单失败: " + e.getMessage());
        }
        JSONObject payResultJsonObj = JSONUtil.parseObj(payResult);
        if (!payResultJsonObj.containsKey("code") || !"00000".equals(payResultJsonObj.getStr("code"))) {
            throw new ServiceException("支付失败，请及时联系管理员");
        }

        // 更新主订单的最后支付ID
        globalOrderService.lambdaUpdate().set(GlobalOrder::getLastPaymentId, globalOrderPayment.getId()).eq(GlobalOrder::getId, globalOrderId).update();

        // 如果已上传线下转账凭证，更新子订单和订单项状态
        GlobalOrderPayment payment = globalOrderPaymentMapper.selectById(globalOrderPayment.getId());
        if (payment != null && payment.getUploadOfflineTransferAt() != null) {
            // 更新子订单状态为待审核
            globalOrderSubService.lambdaUpdate().eq(GlobalOrderSub::getGlobalOrderId, globalOrderId).set(GlobalOrderSub::getOrderStatus, GlobalOrderStatusEnum.TRANSFER_AUDIT).update();

            // 更新订单项状态为待审核并设置支付时间
            globalOrderItemService.lambdaUpdate().eq(GlobalOrderItem::getGlobalOrderId, globalOrderId).set(GlobalOrderItem::getOrderStatus, GlobalOrderStatusEnum.TRANSFER_AUDIT).set(GlobalOrderItem::getOrderPayAt, LocalDateTime.now()).update();
        }

        // 更新支付记录的转账信息
        JSONObject resultFromPayCenter = payResultJsonObj.getJSONObject("result");
        globalOrderPaymentService.lambdaUpdate().set(GlobalOrderPayment::getOfflineTransferInfo, resultFromPayCenter.toString()).eq(GlobalOrderPayment::getId, globalOrderPayment.getId()).update();

        // 构建返回结果
        OfflinePayResultDTO result = new OfflinePayResultDTO();
        result.setBankInfo(new OfflinePayResultDTO.BankInfo().setPayeeName(resultFromPayCenter.getStr("acctName")).setPayeeCard(resultFromPayCenter.getStr("orderAcctNo")).setRevBankName(resultFromPayCenter.getStr("bankName")));
        result.setOrderInfo(new OfflinePayResultDTO.OrderInfo().setOrderId(globalOrderId).setOrderNo(globalOrder.getOrderNo()).setNeedPayAmount(globalOrder.getNeedPayAmount()));
        return result;

    }

    /**
     * 模拟支付结果
     */
    private Object mockPaymentResult(GlobalPayTypeEnum payType, GlobalOrder globalOrder, Long globalOrderId, String tradeNo) {
        // 线上支付直接返回tradeNo
        if (payType != GlobalPayTypeEnum.OFFLINE) {
            return new Dict().set("tradeNo", tradeNo);
        }

        // 线下支付返回模拟的详细信息
        return new OfflinePayResultDTO().setBankInfo(new OfflinePayResultDTO.BankInfo().setPayeeName("模拟收款人").setPayeeCard("6222000000000000000").setRevBankName("模拟银行")).setOrderInfo(new OfflinePayResultDTO.OrderInfo().setOrderId(globalOrderId).setOrderNo(tradeNo).setNeedPayAmount(globalOrder.getNeedPayAmount()));
    }

    /**
     * 线下转账
     *
     * @param offlineTransferDTO 线下转账参数
     */
    @Transactional
    @Override
    public void offlineTransfer(OfflineTransferDTO offlineTransferDTO) {
        // 检查订单
        GlobalOrder globalOrder = globalOrderService.getOne(Wrappers.lambdaQuery(GlobalOrder.class).eq(GlobalOrder::getId, offlineTransferDTO.getOrderId()));
        AssertUtil.assertNotNull(globalOrder, "订单不存在");

        // 检查订单状态，只有待付款,审核中才能上传
        AssertUtil.assertTrue(globalOrder.getOrderStatus() == GlobalOrderStatusEnum.TO_BE_PAID || globalOrder.getOrderStatus() == GlobalOrderStatusEnum.TRANSFER_AUDIT, "只有待付款,审核中才能上传凭证");

        // 检查支付信息
        GlobalOrderPayment globalOrderPayment = globalOrderPaymentMapper.selectOne(Wrappers.lambdaQuery(GlobalOrderPayment.class).eq(GlobalOrderPayment::getGlobalOrderId, offlineTransferDTO.getOrderId()).eq(GlobalOrderPayment::getPayType, GlobalPayTypeEnum.OFFLINE));
        AssertUtil.assertNotNull(globalOrderPayment, "支付信息不存在");

        // 拿到流水号
        String outTradeNo = globalOrderPayment.getOutTradeNo();

        // 判断是否为mock支付模式
        if (!liveServiceProperties.getEnableMockPay()) {
            // 正常调用中台的上传接口
            Object result = payService.offlinePayUpload(outTradeNo, offlineTransferDTO.getOfflineTransferImgList());
            JSONObject resultFromPayCenter = JSONUtil.parseObj(result);
            if (!Objects.equals(resultFromPayCenter.getStr("code"), "00000")) {
                throw new ServiceException("上传凭证失败，请及时联系管理员");
            }
        }

        // 保存图片到payment
        globalOrderPaymentService.lambdaUpdate().set(GlobalOrderPayment::getOfflineTransferImgList, JSONUtil.toJsonStr(offlineTransferDTO.getOfflineTransferImgList())).set(GlobalOrderPayment::getUploadOfflineTransferAt, new Date()).eq(GlobalOrderPayment::getId, globalOrderPayment.getId()).update();

        // 如果订单状态是未支付，更新为审核中
        if (globalOrder.getOrderStatus() == GlobalOrderStatusEnum.TO_BE_PAID) {
            globalOrderService.lambdaUpdate().set(GlobalOrder::getOrderStatus, GlobalOrderStatusEnum.TRANSFER_AUDIT)
                    // 1-审核中，2-审核失败
                    .set(GlobalOrder::getOrderPayStatus, 1).eq(GlobalOrder::getId, globalOrder.getId()).update();
            globalOrderSubService.lambdaUpdate().set(GlobalOrderSub::getOrderStatus, GlobalOrderStatusEnum.TRANSFER_AUDIT).eq(GlobalOrderSub::getGlobalOrderId, globalOrder.getId()).update();
            globalOrderItemService.lambdaUpdate().set(GlobalOrderItem::getOrderStatus, GlobalOrderStatusEnum.TRANSFER_AUDIT).eq(GlobalOrderItem::getGlobalOrderId, globalOrder.getId()).update();
        }
    }

    /**
     * 检查支付是否成功
     *
     * @param orderId@return 支付是否成功: true/false
     */
    @SuppressWarnings("all")
    @Transactional
    @Override
    public Boolean checkPaySuccess(Long orderId) {
        // 先查订单，再查询最新的payment的out_trade_no
        GlobalOrder globalOrder = globalOrderService.getOne(Wrappers.lambdaQuery(GlobalOrder.class).eq(GlobalOrder::getId, orderId));
        AssertUtil.assertTrue(globalOrder != null, "订单不存在");

        // 查最新的订单流水号，因为每次支付后都会更新订单号
        String orderNo = globalOrder.getOrderNo();

        // 如果开启模拟支付，直接走回调成功
        if (liveServiceProperties.getEnableMockPay()) {
            mockPaySuccess(orderNo);
            return true;
        }

        // 正常流程要查一下支付流水
        GlobalOrderPayment orderPayment = globalOrderPaymentMapper.selectOne(Wrappers.lambdaQuery(GlobalOrderPayment.class).eq(GlobalOrderPayment::getGlobalOrderId, globalOrder.getId()).orderByDesc(GlobalOrderPayment::getCreatedAt).last("limit 1"));
        AssertUtil.assertTrue(orderPayment != null, "未找到支付流水");

        // 生产上回调比检查更快，因此如果已经支付，不再往下执行
        if (orderPayment.getPayStatus() == GlobalOrderPaymentStatusEnum.PAID) {
            return true;
        }

        // 这里直接调支付成功的回调，在回调中会去检查中台的支付状态，如果未支付会抛ServiceException
        paySuccess(orderPayment.getOutTradeNo());

        // 到这里肯定是成功了，因为回调里处理了未支付
        return true;
    }

    /**
     * 订单状态数量分组， 店铺中心：显示企业的 个人中心，显示自己的 同时附带用户信息
     *
     * @return 订单状态数量
     */
    @SuppressWarnings("all")
    @Override
    public OrderStatusCountGroupDTO getStatusCountGroup(OrderCenterDTO orderCenterDTO) {
        AuthUser user = AuthUtil.getUser();
        var seatId = user.getSeatId();
        var orgId = user.getOrgId();
        var group = new OrderStatusCountGroupDTO();
        // 前端传是个人还是企业
        boolean ifOrg = orderCenterDTO.getIfOrg();

        // 多线程执行，提升效率
        // ThreadPoolManager.getGlobalBizExecutor();
        ExecutorService executor = ThreadPoolManager.virtualExecutors("order-status-count-group");

        try {
            // 采购-收银台
            CompletableFuture<Long> purchaseCashierProducFuture = supplyAsync(() -> orderCounter.purchase.getCashierProductCount(ifOrg, seatId, orgId, null), executor);
            // 采购-收银台-个人收银台数量
            CompletableFuture<Long> individualPurchaseCashierProductCount = supplyAsync(() -> orderCounter.purchase.getCashierProductCount(true, seatId, orgId, null), executor);
            // 采购-收银台-全店收银台数量
            CompletableFuture<Long> wholeShopPurchaseCashierProductCount = supplyAsync(() -> orderCounter.purchase.getCashierProductCount(false, seatId, orgId, null), executor);
            // 采购-待付款
            CompletableFuture<Long> pendingPaymentFuture = supplyAsync(() -> orderCounter.purchase.getPendingPaymentCount(ifOrg, seatId, orgId, null), executor);
            // 采购-线下转账待审核
            CompletableFuture<Long> offlineTransferPendingReviewFuture = supplyAsync(() -> orderCounter.purchase.getOfflineTransferPendingReviewCount(ifOrg, seatId, orgId, null), executor);
            // 采购-待发货
            CompletableFuture<Long> pendingDeliveryFuture = supplyAsync(() -> orderCounter.purchase.getPendingShipmentCount(ifOrg, seatId, orgId, null), executor);
            // 采购-待收货
            CompletableFuture<Long> pendingReceiptFuture = supplyAsync(() -> orderCounter.purchase.getPendingReceiptCount(ifOrg, seatId, orgId, null), executor);
            // 采购-退款/售后
            CompletableFuture<Long> purchaseRefundFuture = supplyAsync(() -> orderCounter.purchase.getPurchaseRefundCount(ifOrg, seatId, orgId, null), executor);
            // 采购-取消成交数量
            CompletableFuture<Long> purchaseGoodsCancelFuture = supplyAsync(() -> orderCounter.purchase.getGoodsCancelCount(ifOrg, seatId, orgId), executor);
            // 采购-取消成交-路由状态
            CompletableFuture<LiveGoodsBuyerCancelRecordStatusEnum> purchaseGoodsCancelRouteStatusFuture = supplyAsync(() -> orderCounter.purchase.getGoodsCancelRouteStatus(ifOrg, seatId, orgId), executor);
            // 销售-收银台
            CompletableFuture<Long> saleCashierCountFuture = supplyAsync(() -> orderCounter.sale.getCashierProductCount(orgId, null), executor);
            // 销售-待付款
            CompletableFuture<Long> salePendingPaymentFuture = supplyAsync(() -> orderCounter.sale.getPendingPaymentCount(orgId, null), executor);
            // 销售-线下转账待审核
            CompletableFuture<Long> saleOfflineTransferPendingReviewFuture = supplyAsync(() -> orderCounter.sale.getOfflineTransferPendingReviewCount(orgId, null), executor);
            // 销售-待发货
            CompletableFuture<Long> salePendingDeliveryFuture = supplyAsync(() -> orderCounter.sale.getPendingShipmentCount(orgId, null), executor);
            // 销售-待收货
            CompletableFuture<Long> salePendingReceiptFuture = supplyAsync(() -> orderCounter.sale.getPendingReceiptCount(orgId, null), executor);
            // 销售-退款
            CompletableFuture<Long> saleRefundFuture = supplyAsync(() -> orderCounter.sale.getSaleRefundCount(orgId, null), executor);
            // 销售-取消成交数量
            CompletableFuture<Long> saleGoodsCancelFuture = supplyAsync(() -> orderCounter.sale.getGoodsCancelCount(orgId), executor);
            // 销售-取消成交-路由状态
            CompletableFuture<LiveGoodsBuyerCancelRecordStatusEnum> saleGoodsCancelRouteStatusFuture = supplyAsync(() -> orderCounter.sale.getGoodsCancelRouteStatus(orgId), executor);
            // 个人信息
            CompletableFuture<SimpleUserInfoDTO> userInfoFuture = supplyAsync(() -> userInfoService.getSimpleInfoBySeatId(seatId), executor);
            // 消息-未读消息数量
            CompletableFuture<Integer> unreadMessageFuture = supplyAsync(() -> globalSeatMessageDataService.countSeatUnReadMessage(orgId, seatId), executor);

            // 等待所有任务完成
            CompletableFuture.allOf(purchaseCashierProducFuture, individualPurchaseCashierProductCount, wholeShopPurchaseCashierProductCount, pendingPaymentFuture, offlineTransferPendingReviewFuture, pendingDeliveryFuture, pendingReceiptFuture, purchaseRefundFuture, saleCashierCountFuture, saleOfflineTransferPendingReviewFuture, salePendingDeliveryFuture, salePendingReceiptFuture, saleRefundFuture, userInfoFuture, unreadMessageFuture, purchaseGoodsCancelFuture, saleGoodsCancelFuture, saleGoodsCancelRouteStatusFuture, purchaseGoodsCancelRouteStatusFuture).thenAccept(v -> {
                // 采购的
                OrderStatusCountGroupDTO.Purchase purchase = new OrderStatusCountGroupDTO.Purchase();
                purchase.setCashierProductCount(purchaseCashierProducFuture.join());
                purchase.setIndividualProductCount(individualPurchaseCashierProductCount.join());
                purchase.setWholeShopProductCount(wholeShopPurchaseCashierProductCount.join());
                purchase.setPendingPaymentCount(pendingPaymentFuture.join());
                purchase.setOfflineTransferPendingReviewCount(offlineTransferPendingReviewFuture.join());
                purchase.setPendingShipmentCount(pendingDeliveryFuture.join());
                purchase.setPendingReceiptCount(pendingReceiptFuture.join());
                purchase.setRefundAfterSalesCount(purchaseRefundFuture.join());
                purchase.setGoodsCancelCount(purchaseGoodsCancelFuture.join());
                purchase.setGoodsCancelRouteStatus(purchaseGoodsCancelRouteStatusFuture.join());
                countTotalPurchase(purchase);
                group.setPurchase(purchase);
                // 销售的
                OrderStatusCountGroupDTO.Sale sale = new OrderStatusCountGroupDTO.Sale();
                sale.setCashierProductCount(saleCashierCountFuture.join());
                sale.setPendingPaymentCount(salePendingPaymentFuture.join());
                sale.setOfflineTransferPendingReviewCount(saleOfflineTransferPendingReviewFuture.join());
                sale.setPendingShipmentCount(salePendingDeliveryFuture.join());
                sale.setPendingReceiptCount(salePendingReceiptFuture.join());
                sale.setRefundAfterSalesCount(saleRefundFuture.join());
                sale.setGoodsCancelCount(saleGoodsCancelFuture.join());
                sale.setGoodsCancelRouteStatus(saleGoodsCancelRouteStatusFuture.join());
                countTotalSale(sale);
                group.setSale(sale);
                // 个人信息
                group.setUserInfo(userInfoFuture.join());
                // 消息
                OrderStatusCountGroupDTO.Message message = new OrderStatusCountGroupDTO.Message();
                message.setUnreadCount(unreadMessageFuture.join());
                group.setMessage(message);
            }).exceptionally(throwable -> {
                Throwable cause = (throwable instanceof CompletionException) ? throwable.getCause() : throwable;
                log.error("Failed to process order status count", cause);

                if (cause instanceof TimeoutException) {
                    throw new ServiceException("获取订单状态统计超时");
                } else if (cause instanceof RuntimeException) {
                    throw (RuntimeException) cause;
                } else {
                    throw new ServiceException("获取订单状态统计失败");
                }
            }).orTimeout(30, TimeUnit.SECONDS).join();

            // 新订单数量
            countNewOrder(group, ifOrg, seatId, orgId);

            return group;
        } catch (Exception e) {
            log.error("Completion exception in order status count", e);
            throw new ServiceException("获取订单状态统计失败");
        } finally {
            // 关闭线程池
            executor.shutdown();
        }
    }

    @Override
    public OrderStatusCountGroupDTO getUnreadMessage(OrderCenterDTO orderCenterDTO) {
        AuthUser user = AuthUtil.getUser();
        Integer unreadMessageCount = globalSeatMessageDataService.countSeatUnReadMessage(user.getOrgId(), user.getSeatId());
        var group = new OrderStatusCountGroupDTO();
        // 消息
        OrderStatusCountGroupDTO.Message message = new OrderStatusCountGroupDTO.Message();
        message.setUnreadCount(unreadMessageCount);
        group.setMessage(message);
        return group;
    }

    private <T> CompletableFuture<T> supplyAsync(Supplier<T> supplier, Executor executor) {
        return CompletableFuture.supplyAsync(VirtualRunner.wrapper(supplier), executor).orTimeout(8, TimeUnit.SECONDS).whenComplete((result, throwable) -> log.error("Completion exception in order status count", throwable));
    }

    @SuppressWarnings("all")
    private void countNewOrder(OrderStatusCountGroupDTO group, boolean ifOrg, Long seatId, Long orgId) {
        // 处理未读
        String redPointKey = ifOrg ? LiveOrderCacheKeys.SHOP_CENTER_RED_POINT.getKey() : LiveOrderCacheKeys.PERSONAL_CENTER_RED_POINT.getKey();
        String idString = ifOrg ? orgId.toString() : seatId.toString();
        HashMap<RedPointTypeEnum, Date> viewTimeMap = new HashMap<>();
        for (RedPointTypeEnum typeEnum : RedPointTypeEnum.values()) {
            String typeEnumString = typeEnum.toString();
            String redisKey = buildKey(redPointKey, typeEnumString, idString);
            String viewTimeString = stringRedisTemplate.opsForValue().get(redisKey);
            viewTimeMap.put(typeEnum, StringUtils.isBlank(viewTimeString) ? null : DateUtil.parse(viewTimeString, DatePattern.NORM_DATETIME_FORMAT));
        }

        ExecutorService executor = ThreadPoolManager.virtualExecutors("order-status-count-new-group");
        try {
            // 采购-收银台
            CompletableFuture<Long> purchaseCashierProducFuture = supplyAsync(() -> orderCounter.purchase.getCashierProductCount(ifOrg, seatId, orgId, viewTimeMap.get(RedPointTypeEnum.purchaseCashierProductCount)), executor);
            // 采购-收银台-个人收银台数量
            CompletableFuture<Long> individualPurchaseCashierProductCount = supplyAsync(() -> orderCounter.purchase.getCashierProductCount(true, seatId, orgId, viewTimeMap.get(RedPointTypeEnum.individualPurchaseCashierProductCount)), executor);
            // 采购-收银台-全店收银台数量
            CompletableFuture<Long> wholeShopPurchaseCashierProductCount = supplyAsync(() -> orderCounter.purchase.getCashierProductCount(false, seatId, orgId, viewTimeMap.get(RedPointTypeEnum.wholeShopPurchaseCashierProductCount)), executor);
            // 采购-待付款
            CompletableFuture<Long> pendingPaymentFuture = supplyAsync(() -> orderCounter.purchase.getPendingPaymentCount(ifOrg, seatId, orgId, viewTimeMap.get(RedPointTypeEnum.purchasePendingPaymentCount)), executor);
            // 采购-线下转账待审核
            CompletableFuture<Long> offlineTransferPendingReviewFuture = supplyAsync(() -> orderCounter.purchase.getOfflineTransferPendingReviewCount(ifOrg, seatId, orgId, viewTimeMap.get(RedPointTypeEnum.purchaseOfflineTransferPendingReviewCount)), executor);
            // 采购-待发货
            CompletableFuture<Long> pendingDeliveryFuture = supplyAsync(() -> orderCounter.purchase.getPendingShipmentCount(ifOrg, seatId, orgId, viewTimeMap.get(RedPointTypeEnum.purchasePendingShipmentCount)), executor);
            // 采购-待收货
            CompletableFuture<Long> pendingReceiptFuture = supplyAsync(() -> orderCounter.purchase.getPendingReceiptCount(ifOrg, seatId, orgId, viewTimeMap.get(RedPointTypeEnum.purchasePendingReceiptCount)), executor);
            // 采购-退款/售后
            CompletableFuture<Long> purchaseRefundFuture = supplyAsync(() -> orderCounter.purchase.getPurchaseRefundCount(ifOrg, seatId, orgId, viewTimeMap.get(RedPointTypeEnum.purchaseRefundAfterSalesCount)), executor);

            // 销售-收银台
            CompletableFuture<Long> saleCashierProductFuture = supplyAsync(() -> orderCounter.sale.getCashierProductCount(orgId, viewTimeMap.get(RedPointTypeEnum.saleCashierProductCount)), executor);
            // 销售-待付款
            CompletableFuture<Long> salePendingPaymentFuture = supplyAsync(() -> orderCounter.sale.getPendingPaymentCount(orgId, viewTimeMap.get(RedPointTypeEnum.salePendingPaymentCount)), executor);
            // 销售-线下转账待审核
            CompletableFuture<Long> saleOfflineTransferPendingReviewFuture = supplyAsync(() -> orderCounter.sale.getOfflineTransferPendingReviewCount(orgId, viewTimeMap.get(RedPointTypeEnum.saleOfflineTransferPendingReviewCount)), executor);
            // 销售-待发货
            CompletableFuture<Long> salePendingDeliveryFuture = supplyAsync(() -> orderCounter.sale.getPendingShipmentCount(orgId, viewTimeMap.get(RedPointTypeEnum.salePendingShipmentCount)), executor);
            // 销售-待收货
            CompletableFuture<Long> salePendingReceiptFuture = supplyAsync(() -> orderCounter.sale.getPendingReceiptCount(orgId, viewTimeMap.get(RedPointTypeEnum.salePendingReceiptCount)), executor);
            // 销售-退款
            CompletableFuture<Long> saleRefundFuture = supplyAsync(() -> orderCounter.sale.getSaleRefundCount(orgId, viewTimeMap.get(RedPointTypeEnum.saleRefundAfterSalesCount)), executor);

            // 等待所有任务完成
            CompletableFuture.allOf(pendingPaymentFuture, offlineTransferPendingReviewFuture, pendingDeliveryFuture, pendingReceiptFuture, purchaseRefundFuture, saleCashierProductFuture, saleOfflineTransferPendingReviewFuture, salePendingDeliveryFuture, salePendingReceiptFuture, saleRefundFuture).thenAccept(v -> {
                // 采购的
                OrderStatusCountGroupDTO.Purchase purchase = new OrderStatusCountGroupDTO.Purchase();
                purchase.setCashierProductCount(purchaseCashierProducFuture.join());
                purchase.setIndividualProductCount(individualPurchaseCashierProductCount.join());
                purchase.setWholeShopProductCount(wholeShopPurchaseCashierProductCount.join());
                purchase.setPendingPaymentCount(pendingPaymentFuture.join());
                purchase.setOfflineTransferPendingReviewCount(offlineTransferPendingReviewFuture.join());
                purchase.setPendingShipmentCount(pendingDeliveryFuture.join());
                purchase.setPendingReceiptCount(pendingReceiptFuture.join());
                purchase.setRefundAfterSalesCount(purchaseRefundFuture.join());
                countTotalPurchase(purchase);
                group.setPurchaseNew(purchase);
                // 销售的
                OrderStatusCountGroupDTO.Sale sale = new OrderStatusCountGroupDTO.Sale();
                sale.setPendingPaymentCount(salePendingPaymentFuture.join());
                sale.setCashierProductCount(saleCashierProductFuture.join());
                sale.setOfflineTransferPendingReviewCount(saleOfflineTransferPendingReviewFuture.join());
                sale.setPendingShipmentCount(salePendingDeliveryFuture.join());
                sale.setPendingReceiptCount(salePendingReceiptFuture.join());
                sale.setRefundAfterSalesCount(saleRefundFuture.join());
                countTotalSale(sale);
                group.setSaleNew(sale);
            }).orTimeout(30, TimeUnit.SECONDS).join();
        } catch (Exception e) {
            log.error("获取订单数量统计时发生异常", e);
            throw e;
        } finally {
            executor.shutdown();
        }
    }

    /**
     * 计算采购数量，不包含退款数量
     *
     * @param purchase
     */
    private void countTotalPurchase(OrderStatusCountGroupDTO.Purchase purchase) {
        purchase.setTotalCount(purchase.getPendingPaymentCount() +
                purchase.getOfflineTransferPendingReviewCount() + purchase.getPendingShipmentCount() + purchase.getPendingReceiptCount() + purchase.getCashierProductCount());
    }

    /**
     * 计算销售数量，不包含退款数量
     *
     * @param sale
     */
    private void countTotalSale(OrderStatusCountGroupDTO.Sale sale) {
        sale.setTotalCount(sale.getPendingPaymentCount()+
                sale.getCashierProductCount() + sale.getOfflineTransferPendingReviewCount() + sale.getPendingShipmentCount() + sale.getPendingReceiptCount());
    }
}
