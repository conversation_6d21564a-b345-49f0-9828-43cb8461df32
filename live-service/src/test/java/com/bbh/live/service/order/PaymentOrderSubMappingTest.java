package com.bbh.live.service.order;

import cn.hutool.json.JSONUtil;
import com.bbh.live.service.order.dto.PaymentOrderSubMappingDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 支付流水与子订单映射关系测试
 * 
 * <AUTHOR>
 */
@SpringBootTest
public class PaymentOrderSubMappingTest {
    
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Test
    public void testPaymentOrderSubMapping() {
        // 模拟数据
        String outTradeNo = "TEST_TRADE_NO_" + System.currentTimeMillis();
        Long globalOrderId = 12345L;

        Map<Long, String> subOrderNoMap = new HashMap<>();
        subOrderNoMap.put(1001L, "SUB_ORDER_NO_1001");
        subOrderNoMap.put(1002L, "SUB_ORDER_NO_1002");
        subOrderNoMap.put(1003L, "SUB_ORDER_NO_1003");

        // 创建映射DTO
        PaymentOrderSubMappingDTO mappingDTO = new PaymentOrderSubMappingDTO(
                outTradeNo,
                subOrderNoMap,
                globalOrderId,
                System.currentTimeMillis()
        );

        // 存入缓存 - 使用String结构存储JSON
        String cacheKey = "payment:order_sub_mapping:" + outTradeNo;
        String jsonValue = JSONUtil.toJsonStr(mappingDTO);
        stringRedisTemplate.opsForValue().set(cacheKey, jsonValue, 1, TimeUnit.MINUTES);

        // 从缓存读取
        String retrievedJson = stringRedisTemplate.opsForValue().get(cacheKey);
        PaymentOrderSubMappingDTO retrievedDTO = JSONUtil.toBean(retrievedJson, PaymentOrderSubMappingDTO.class);

        // 验证数据
        assert retrievedDTO != null;
        assert outTradeNo.equals(retrievedDTO.getOutTradeNo());
        assert globalOrderId.equals(retrievedDTO.getGlobalOrderId());
        assert subOrderNoMap.equals(retrievedDTO.getSubOrderNoMap());

        System.out.println("测试通过：支付流水与子订单映射关系缓存功能正常");
        System.out.println("原始数据：" + mappingDTO);
        System.out.println("读取数据：" + retrievedDTO);

        // 清理缓存
        stringRedisTemplate.delete(cacheKey);
    }
}
