# 支付回调更新order_sub_no功能实现总结

## 功能概述

实现了支付回调时正确更新order_sub表的order_sub_no字段的功能，确保使用生成payment时对应的order_sub_no值。

## 核心思路

使用Redis缓存来建立payment的outTradeNo与order_sub的id和order_sub_no之间的映射关系：

1. **拉起支付时**：缓存payment对应的order_sub信息
2. **支付成功回调时**：从缓存获取映射关系并更新order_sub_no
3. **支付完成后**：清理缓存

## 技术实现

### 1. 数据结构

```java
public class PaymentOrderSubMappingDTO {
    private String outTradeNo;              // 支付流水号
    private Map<Long, String> subOrderNoMap; // 子订单ID与子订单号的映射关系
    private Long globalOrderId;             // 总订单ID
    private Long createTime;                // 创建时间戳
}
```

### 2. 缓存策略

- **存储结构**：Redis String（直接存储JSON字符串）
- **缓存Key**：`payment:order_sub_mapping:{outTradeNo}`
- **过期时间**：7天
- **优势**：比Hash结构更高效，减少内存开销

### 3. 关键代码修改

#### 创建订单时缓存映射关系
```java
// OrderV2ServiceImpl.saveOrder()
Map<Long, String> subOrderNoMap = new HashMap<>();
for (GlobalOrderSub orderSub : preparedOrder.getOrderSubList()) {
    subOrderNoMap.put(orderSub.getId(), orderSub.getOrderSubNo());
}

String cacheKey = "payment:order_sub_mapping:" + globalOrder.getOrderNo();
PaymentOrderSubMappingDTO mappingDTO = new PaymentOrderSubMappingDTO(
        globalOrder.getOrderNo(), subOrderNoMap, orderId, System.currentTimeMillis());
String jsonValue = JSONUtil.toJsonStr(mappingDTO);
stringRedisTemplate.opsForValue().set(cacheKey, jsonValue, 7, TimeUnit.DAYS);
```

#### 重新拉起支付时更新缓存
```java
// OrderV2ServiceImpl.pay()
Map<Long, String> subOrderNoMap = new HashMap<>();
for (GlobalOrderSub orderSub : globalOrderSubList) {
    String subOrderNo = orderEntityBuilder.buildOrderNo(true);
    orderSub.setOrderSubNo(subOrderNo);
    subOrderNoMap.put(orderSub.getId(), subOrderNo);
    // 更新数据库...
}

// 缓存新的映射关系
String cacheKey = "payment:order_sub_mapping:" + newTradeNo;
PaymentOrderSubMappingDTO mappingDTO = new PaymentOrderSubMappingDTO(
        newTradeNo, subOrderNoMap, globalOrderId, System.currentTimeMillis());
String jsonValue = JSONUtil.toJsonStr(mappingDTO);
stringRedisTemplate.opsForValue().set(cacheKey, jsonValue, 7, TimeUnit.DAYS);
```

#### 支付成功回调时更新order_sub_no
```java
// OrderV2ServiceImpl.orderPaid()
String cacheKey = "payment:order_sub_mapping:" + actualOrderNo;
Map<Long, String> subOrderNoMap = null;
try {
    String jsonValue = stringRedisTemplate.opsForValue().get(cacheKey);
    if (jsonValue != null) {
        PaymentOrderSubMappingDTO mappingDTO = JSONUtil.toBean(jsonValue, PaymentOrderSubMappingDTO.class);
        subOrderNoMap = mappingDTO.getSubOrderNoMap();
    }
} catch (Exception e) {
    LogExUtil.errorLog("从缓存获取order_sub_no映射关系失败", e);
}

// 更新order_sub时使用缓存的order_sub_no
for (GlobalOrderSub orderSub : orderSubList) {
    GlobalOrderSub update = new GlobalOrderSub();
    // ... 其他字段更新
    
    if (subOrderNoMap != null && subOrderNoMap.containsKey(orderSub.getId())) {
        String cachedOrderSubNo = subOrderNoMap.get(orderSub.getId());
        update.setOrderSubNo(cachedOrderSubNo);
    }
    
    orderSubUpdateList.add(update);
}

// 支付成功后清理缓存
stringRedisTemplate.delete(cacheKey);
```

## 优化亮点

1. **存储结构优化**：从Hash结构改为String结构，提高性能
2. **内存优化**：避免Hash结构的额外开销
3. **容错设计**：缓存操作失败不影响主流程
4. **完整的生命周期管理**：创建、使用、清理

## 测试验证

创建了`PaymentOrderSubMappingTest`测试类验证缓存功能的正确性。

## 文件清单

1. **核心实现**：`OrderV2ServiceImpl.java`
2. **数据结构**：`PaymentOrderSubMappingDTO.java`
3. **测试文件**：`PaymentOrderSubMappingTest.java`
4. **文档**：`payment-order-sub-mapping.md`

## 部署注意事项

1. 确保Redis服务正常运行
2. 注意缓存过期时间设置（当前为7天）
3. 监控缓存命中率和错误日志
4. 建议在测试环境充分验证后再部署到生产环境
