# 支付回调更新order_sub_no功能实现

## 问题描述

现在支付回调进来后，会更新order和order_sub的order_no字段，但是order_sub的order_sub_no也要进行更新，得用生成payment时对应的order_sub_no来更新。

## 解决方案

采用Redis缓存的方式来解决这个问题：

1. **拉起支付时缓存payment对应的sub信息**：在创建订单和重新拉起支付时，将payment的outTradeNo与对应的order_sub的id和order_sub_no映射关系存入Redis缓存。

2. **支付成功回调后从缓存取出映射关系**：在支付成功回调时，根据payment的outTradeNo从缓存中获取对应的order_sub映射关系。

3. **批量更新order_sub表**：使用获取到的order_sub_no更新对应的order_sub记录。

## 实现细节

### 1. 数据结构设计

创建了`PaymentOrderSubMappingDTO`类来存储映射关系：

```java
public class PaymentOrderSubMappingDTO {
    private String outTradeNo;              // 支付流水号
    private Map<Long, String> subOrderNoMap; // 子订单ID与子订单号的映射关系
    private Long globalOrderId;             // 总订单ID
    private Long createTime;                // 创建时间戳
}
```

### 2. 缓存策略

- **缓存Key格式**：`payment:order_sub_mapping:{outTradeNo}`
- **缓存时间**：7天（足够覆盖支付回调的时间窗口）
- **存储结构**：使用Redis Hash结构，field为"mapping"，value为序列化的DTO对象

### 3. 代码修改点

#### 3.1 创建订单时缓存映射关系

在`OrderV2ServiceImpl.saveOrder()`方法中添加：

```java
// 缓存order_sub的id和order_sub_no映射关系
Map<Long, String> subOrderNoMap = new HashMap<>();
for (GlobalOrderSub orderSub : preparedOrder.getOrderSubList()) {
    subOrderNoMap.put(orderSub.getId(), orderSub.getOrderSubNo());
}

// 存入缓存
String cacheKey = "payment:order_sub_mapping:" + globalOrder.getOrderNo();
PaymentOrderSubMappingDTO mappingDTO = new PaymentOrderSubMappingDTO(
        globalOrder.getOrderNo(), subOrderNoMap, orderId, System.currentTimeMillis());
redisHashService.putWithExpire(cacheKey, "mapping", mappingDTO, 7, TimeUnit.DAYS);
```

#### 3.2 重新拉起支付时更新缓存

在`OrderV2ServiceImpl.pay()`方法中添加：

```java
// 缓存payment对应的order_sub信息
Map<Long, String> subOrderNoMap = new HashMap<>();
for (GlobalOrderSub orderSub : globalOrderSubList) {
    String subOrderNo = orderEntityBuilder.buildOrderNo(true);
    orderSub.setOrderSubNo(subOrderNo);
    subOrderNoMap.put(orderSub.getId(), subOrderNo);
    // ... 更新数据库
}

// 存入缓存
String cacheKey = "payment:order_sub_mapping:" + newTradeNo;
PaymentOrderSubMappingDTO mappingDTO = new PaymentOrderSubMappingDTO(
        newTradeNo, subOrderNoMap, globalOrderId, System.currentTimeMillis());
redisHashService.putWithExpire(cacheKey, "mapping", mappingDTO, 7, TimeUnit.DAYS);
```

#### 3.3 支付成功回调时更新order_sub_no

在`OrderV2ServiceImpl.orderPaid()`方法中添加：

```java
// 从缓存获取payment对应的order_sub_no映射关系
String cacheKey = "payment:order_sub_mapping:" + actualOrderNo;
Map<Long, String> subOrderNoMap = null;
try {
    PaymentOrderSubMappingDTO mappingDTO = redisHashService.get(cacheKey, "mapping", PaymentOrderSubMappingDTO.class);
    if (mappingDTO != null) {
        subOrderNoMap = mappingDTO.getSubOrderNoMap();
    }
} catch (Exception e) {
    LogExUtil.errorLog("从缓存获取order_sub_no映射关系失败", e);
}

// 更新order_sub时使用缓存的order_sub_no
for (GlobalOrderSub orderSub : orderSubList) {
    GlobalOrderSub update = new GlobalOrderSub();
    // ... 其他字段更新
    
    // 从缓存中获取对应的order_sub_no并更新
    if (subOrderNoMap != null && subOrderNoMap.containsKey(orderSub.getId())) {
        String cachedOrderSubNo = subOrderNoMap.get(orderSub.getId());
        update.setOrderSubNo(cachedOrderSubNo);
    }
    
    orderSubUpdateList.add(update);
}

// 支付成功后清理缓存
redisHashService.remove(cacheKey);
```

## 优势

1. **数据一致性**：确保order_sub_no使用的是生成payment时对应的值
2. **性能优化**：使用Redis缓存，避免复杂的数据库查询
3. **容错性**：即使缓存失败，也不会影响原有的支付流程
4. **可维护性**：代码结构清晰，易于理解和维护

## 注意事项

1. **缓存过期时间**：设置为7天，需要根据实际业务场景调整
2. **异常处理**：缓存操作都有异常处理，不会影响主流程
3. **日志记录**：详细的日志记录便于问题排查
4. **清理机制**：支付成功后主动清理缓存，避免内存浪费

## 测试

创建了`PaymentOrderSubMappingTest`测试类来验证缓存功能的正确性。
